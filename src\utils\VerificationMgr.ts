/**
 * 统一验证管理器
 * 支持在 Geetest 和 Cloudflare Turnstile 之间切换
 */

import { GeetestMgr, GEETEST_TYPE } from "./GeetestMgr";
import { CloudflareMgr, CF_TURNSTILE_TYPE } from "./CloudflareMgr";

// 验证类型枚举
export enum VERIFICATION_TYPE {
  GEETEST = "geetest",
  CLOUDFLARE = "cloudflare",
}

// 验证场景类型 (统一接口)
export const VERIFICATION_SCENE_TYPE = {
  password_login: "password_login",
  phone_code_login: "phone_code_login",
  google_facebook_login: "google_facebook_login",
  forget_password: "forget_password",
  first_password: "first_password",
  first_pay_password: "first_pay_password",
  change_pay_password: "change_pay_password",
  bind_withdraw_account: "bind_withdraw_account",
  change_withdraw_account: "change_withdraw_account",
  withdraw: "withdraw",
  bind_pt_phone: "bind_pt_phone",
  change_pt_phone: "change_pt_phone",
  phone_login_code: "phone_login_code",
  forget_password_code: "forget_password_code",
  change_pay_password_code: "change_pay_password_code",
  bind_withdraw_account_code: "bind_withdraw_account_code",
  change_withdraw_account_code: "change_withdraw_account_code",
  bind_pt_phone_code: "bind_pt_phone_code",
  change_pt_phone_code: "change_pt_phone_code",
};

// 验证结果接口
export interface VerificationResult {
  success: boolean;
  type: VERIFICATION_TYPE;
  data?: {
    // Geetest 返回数据
    geetest_guard?: string;
    userInfo?: string;
    geetest_captcha?: string;
    buds?: string;
    // Cloudflare 返回数据 (更新为新的属性名)
    "cf-token"?: string;
    "cf-scene"?: string;
    // 保持向后兼容的旧属性名
    cf_token?: string;
    cf_type?: string;
  };
}

// 验证配置接口
export interface VerificationConfig {
  type: VERIFICATION_TYPE;
  containerId?: string; // 用于 Cloudflare 的容器ID
  phone?: string; // 用于 Geetest 的手机号
  seamless?: boolean; // 是否使用无感验证（仅对 Cloudflare 有效）
}

export class VerificationMgr {
  private static _instance: VerificationMgr;
  private currentType: VERIFICATION_TYPE;

  static get instance() {
    if (this._instance) {
      return this._instance;
    }
    this._instance = new VerificationMgr();
    return this._instance;
  }

  constructor() {
    // 从环境变量或配置中获取默认验证类型
    this.currentType = this.getDefaultVerificationType();
  }

  /**
   * 获取默认验证类型
   */
  private getDefaultVerificationType(): VERIFICATION_TYPE {
    // 从环境变量读取配置
    const envType = import.meta.env.VITE_VERIFICATION_TYPE;

    if (envType === "cloudflare") {
      return VERIFICATION_TYPE.CLOUDFLARE;
    } else if (envType === "geetest") {
      return VERIFICATION_TYPE.GEETEST;
    }

    // 默认使用 Geetest (保持向后兼容)
    return VERIFICATION_TYPE.GEETEST;
  }

  /**
   * 设置验证类型
   */
  setVerificationType(type: VERIFICATION_TYPE): void {
    this.currentType = type;
    console.log(`🔄 Verification type switched to: ${type}`);
  }

  /**
   * 获取当前验证类型
   */
  getCurrentVerificationType(): VERIFICATION_TYPE {
    return this.currentType;
  }

  /**
   * 执行验证
   * @param sceneType 验证场景类型
   * @param callback 回调函数
   * @param config 验证配置
   * 验证码的是有感的，提交的部分是无感的
   *
   */
  async verify(
    sceneType: string,
    callback: (result: VerificationResult | false) => void,
    config?: Partial<VerificationConfig>
  ): Promise<void> {
    const verificationType = config?.type || this.currentType;

    console.log(`🔐 Starting verification: ${verificationType} - ${sceneType}`);

    try {
      switch (verificationType) {
        case VERIFICATION_TYPE.GEETEST:
          await this.executeGeetestVerification(sceneType, callback, config);
          break;

        case VERIFICATION_TYPE.CLOUDFLARE:
          await this.executeCloudflareVerification(sceneType, callback, config);
          break;

        default:
          console.error(`❌ Unsupported verification type: ${verificationType}`);
          callback(false);
      }
    } catch (error) {
      console.error(`❌ Verification error (${verificationType}):`, error);
      callback(false);
    }
  }

  /**
   * 执行 Geetest 验证
   */
  private async executeGeetestVerification(
    sceneType: string,
    callback: (result: VerificationResult | false) => void,
    config?: Partial<VerificationConfig>
  ): Promise<void> {
    const geetestCallback = (result: any) => {
      if (result === false) {
        callback(false);
        return;
      }

      const verificationResult: VerificationResult = {
        success: true,
        type: VERIFICATION_TYPE.GEETEST,
        data: {
          geetest_guard: result.geetest_guard || "",
          userInfo: result.userInfo || "",
          geetest_captcha: result.geetest_captcha || "",
          buds: result.buds || "64",
        },
      };

      callback(verificationResult);
    };

    // 调用 Geetest 验证
    GeetestMgr.instance.geetest_device(sceneType, geetestCallback, config?.phone);
  }

  /**
   * 执行 Cloudflare 验证
   */
  /**
   * 将场景类型字符串映射到 CF_TURNSTILE_TYPE 枚举
   */
  private mapSceneTypeToCFType(sceneType: string): CF_TURNSTILE_TYPE {
    // 创建反向映射，从枚举值到枚举键
    const cfTypeMap: Record<string, CF_TURNSTILE_TYPE> = {};
    Object.values(CF_TURNSTILE_TYPE).forEach((value) => {
      cfTypeMap[value] = value as CF_TURNSTILE_TYPE;
    });

    // 如果直接匹配到枚举值，返回对应的枚举
    if (cfTypeMap[sceneType]) {
      return cfTypeMap[sceneType];
    }

    // 默认场景映射
    const sceneMapping: Record<string, CF_TURNSTILE_TYPE> = {
      login: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
      register: CF_TURNSTILE_TYPE.LOGIN_PHONE_GET_CODE,
      forgot_password: CF_TURNSTILE_TYPE.FORGET_PW_SUBMIT,
      change_password: CF_TURNSTILE_TYPE.MODIFY_LOGIN_PW_SUBMIT,
      withdraw: CF_TURNSTILE_TYPE.WITHDRAWAL_SUBMIT,
      kyc: CF_TURNSTILE_TYPE.KYC_SUBMIT,
      bind_phone: CF_TURNSTILE_TYPE.BIND_PHONE_SUBMIT,
      change_phone: CF_TURNSTILE_TYPE.MODIFY_PHONE_SUBMIT,
    };

    return sceneMapping[sceneType] || CF_TURNSTILE_TYPE.LOGIN_SUBMIT;
  }

  private async executeCloudflareVerification(
    sceneType: string,
    callback: (result: VerificationResult | false) => void,
    config?: Partial<VerificationConfig>
  ): Promise<void> {
    // 将字符串场景类型映射到 CF_TURNSTILE_TYPE 枚举
    const cfType = this.mapSceneTypeToCFType(sceneType);

    // 新版本使用弹窗API方式
    try {
      const { showCloudflareVerify } = await import("./CloudflareVerifyAPI");

      const verifyOptions: any = {
        cfType,
        title: "Security Verification",
        description: "Please complete the security verification to continue.",
      };

      // 如果配置中有自定义选项，添加到验证选项中
      if (config?.containerId) {
        verifyOptions.containerId = config.containerId;
      }

      const result = await showCloudflareVerify(verifyOptions);

      if (result.success) {
        const verificationResult: VerificationResult = {
          success: true,
          type: VERIFICATION_TYPE.CLOUDFLARE,
          data: {
            // 新的属性名
            "cf-token": result["cf-token"] || result.token || "",
            "cf-scene": result["cf-scene"] || result.cfType || sceneType,
            // 保持向后兼容的旧属性名
            cf_token: result["cf-token"] || result.token || "",
            cf_type: result["cf-scene"] || result.cfType || sceneType,
          },
        };
        callback(verificationResult);
      } else {
        callback(false);
      }
    } catch (error) {
      console.error("Cloudflare verification failed:", error);
      callback(false);
    }
  }

  /**
   * 重置验证组件
   */
  reset(): void {
    switch (this.currentType) {
      case VERIFICATION_TYPE.GEETEST:
        // Geetest 没有直接的 reset 方法，通常重新调用验证即可
        break;

      case VERIFICATION_TYPE.CLOUDFLARE:
        // 新版本使用弹窗方式，无需直接重置
        console.log("Cloudflare verification reset - using dialog mode");
        break;
    }
  }

  /**
   * 移除验证组件
   */
  remove(): void {
    switch (this.currentType) {
      case VERIFICATION_TYPE.GEETEST:
        // Geetest 没有直接的 remove 方法
        break;

      case VERIFICATION_TYPE.CLOUDFLARE:
        // 新版本使用弹窗方式，无需直接移除
        console.log("Cloudflare verification remove - using dialog mode");
        break;
    }
  }

  /**
   * 获取当前验证 token
   */
  getToken(): string {
    switch (this.currentType) {
      case VERIFICATION_TYPE.GEETEST:
        // Geetest 的 token 通常在回调中返回，这里返回空字符串
        return "";

      case VERIFICATION_TYPE.CLOUDFLARE:
        // 新版本的 token 在验证回调中返回，这里返回空字符串
        return "";

      default:
        return "";
    }
  }

  /**
   * 清除当前验证 token
   */
  clearToken(): void {
    switch (this.currentType) {
      case VERIFICATION_TYPE.GEETEST:
        // Geetest 没有持久化 token，无需清除
        break;

      case VERIFICATION_TYPE.CLOUDFLARE:
        // 新版本的 token 不持久化，无需清除
        break;
    }
  }

  /**
   * 检查验证类型是否可用
   */
  async isVerificationTypeAvailable(type: VERIFICATION_TYPE): Promise<boolean> {
    switch (type) {
      case VERIFICATION_TYPE.GEETEST:
        // 检查 Geetest 脚本是否加载
        return typeof window !== "undefined" && !!(window as any).initGeeGuard;

      case VERIFICATION_TYPE.CLOUDFLARE:
        // 检查 Cloudflare Turnstile 脚本是否可以加载
        try {
          const mgr = CloudflareMgr.instance;
          // 尝试初始化脚本
          await mgr["initTurnstileScript"]();
          return true;
        } catch {
          return false;
        }

      default:
        return false;
    }
  }

  /**
   * 获取验证类型的显示名称
   */
  getVerificationTypeName(type?: VERIFICATION_TYPE): string {
    const targetType = type || this.currentType;

    switch (targetType) {
      case VERIFICATION_TYPE.GEETEST:
        return "Geetest";
      case VERIFICATION_TYPE.CLOUDFLARE:
        return "Cloudflare Turnstile";
      default:
        return "Unknown";
    }
  }

  /**
   * 测试验证兼容性 (仅用于开发调试)
   */
  public async testVerificationCompatibility(): Promise<void> {
    console.log("🧪 Testing verification compatibility...");

    // 测试 Geetest 可用性
    const geetestAvailable = await this.isVerificationTypeAvailable(VERIFICATION_TYPE.GEETEST);
    console.log(`Geetest available: ${geetestAvailable ? "✅" : "❌"}`);

    // 测试 Cloudflare 可用性
    const cloudflareAvailable = await this.isVerificationTypeAvailable(
      VERIFICATION_TYPE.CLOUDFLARE
    );
    console.log(`Cloudflare available: ${cloudflareAvailable ? "✅" : "❌"}`);

    // 显示当前配置
    console.log(`Current verification type: ${this.getVerificationTypeName()}`);
    console.log(`Environment config: ${import.meta.env.VITE_VERIFICATION_TYPE || "not set"}`);

    console.log("🧪 Verification compatibility test completed.");
  }

  /**
   * 获取验证结果的统一格式 (用于向后兼容)
   */
  public normalizeVerificationResult(result: VerificationResult): any {
    if (!result.success || !result.data) {
      return { success: false };
    }

    const normalized: any = { success: true };

    if (result.type === VERIFICATION_TYPE.GEETEST) {
      // Geetest 格式
      normalized.geetest_guard = result.data.geetest_guard;
      normalized.userInfo = result.data.userInfo;
      normalized.geetest_captcha = result.data.geetest_captcha;
      normalized.buds = result.data.buds;
    } else if (result.type === VERIFICATION_TYPE.CLOUDFLARE) {
      // Cloudflare 格式 (支持新旧属性名)
      normalized["cf-token"] = result.data["cf-token"] || result.data.cf_token;
      normalized["cf-scene"] = result.data["cf-scene"] || result.data.cf_type;
      // 向后兼容的旧属性名
      normalized.cf_token = result.data["cf-token"] || result.data.cf_token;
      normalized.cf_type = result.data["cf-scene"] || result.data.cf_type;
    }

    return normalized;
  }
}

// 导出便捷函数
export const verificationMgr = VerificationMgr.instance;

/**
 * 便捷的验证函数
 * @param sceneType 验证场景类型
 * @param callback 回调函数
 * @param config 验证配置
 */
export const executeVerification = async (
  sceneType: string,
  callback: (result: VerificationResult | false) => void,
  config?: Partial<VerificationConfig>
): Promise<void> => {
  return verificationMgr.verify(sceneType, callback, config);
};

/**
 * 切换验证类型的便捷函数
 * @param type 验证类型
 */
export const switchVerificationType = (type: VERIFICATION_TYPE): void => {
  verificationMgr.setVerificationType(type);
};
